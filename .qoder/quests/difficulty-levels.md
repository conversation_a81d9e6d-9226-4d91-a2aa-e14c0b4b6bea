# Tetris Game Difficulty Levels Design

## Overview

This design document outlines the implementation of a three-tier difficulty level system for the existing Tetris game project. The system will provide players with Easy, Medium, and Hard difficulty options, each offering distinct gameplay mechanics to accommodate different skill levels and enhance player engagement.

## Architecture

### Difficulty Level System Structure

```mermaid
graph TD
    A[Game Start] --> B[Difficulty Selection Menu]
    B --> C[Easy Mode]
    B --> D[Medium Mode] 
    B --> E[Hard Mode]
    C --> F[Game Instance with Easy Config]
    D --> G[Game Instance with Medium Config]
    E --> H[Game Instance with Hard Config]
    F --> I[Game Loop]
    G --> I
    H --> I
```

### Configuration Management

```mermaid
classDiagram
    class DifficultyConfig {
        +string name
        +number initialSpeed
        +number speedIncrement
        +number linesPerLevel
        +array~string~ enabledPieces
        +number previewCount
        +boolean ghostPiece
        +ScoreMultiplier scoreMultiplier
    }
    
    class TetrisGame {
        +DifficultyConfig currentDifficulty
        +selectDifficulty(level)
        +applyDifficultySettings()
        +updateGameSpeed()
    }
    
    DifficultyConfig --> TetrisGame : configures
```

## Difficulty Level Specifications

### Easy Level (初级)
**Target Audience**: Beginners and casual players

| Parameter | Value | Description |
|-----------|-------|-------------|
| Initial Drop Speed | 1500ms | Slower piece falling |
| Speed Increment | 50ms per level | Gradual speed increase |
| Lines Per Level | 15 lines | More lines needed to advance |
| Enabled Pieces | All 7 pieces | Full piece variety |
| Preview Pieces | 2 pieces | Shows next 2 pieces |
| Ghost Piece | Enabled | Shadow showing drop position |
| Score Multiplier | 1.0x | Standard scoring |

**Special Features**:
- Extended piece preview system
- Ghost piece assistance for positioning
- Gentle difficulty progression
- Forgiving timing mechanics

### Medium Level (中级)
**Target Audience**: Intermediate players seeking balanced challenge

| Parameter | Value | Description |
|-----------|-------|-------------|
| Initial Drop Speed | 1000ms | Standard falling speed |
| Speed Increment | 80ms per level | Moderate speed increase |
| Lines Per Level | 10 lines | Standard progression |
| Enabled Pieces | All 7 pieces | Full piece variety |
| Preview Pieces | 1 piece | Shows next 1 piece |
| Ghost Piece | Optional | Player can toggle on/off |
| Score Multiplier | 1.2x | Bonus scoring |

**Special Features**:
- Classic Tetris experience
- Balanced progression curve
- Optional assistance features
- Standard scoring with slight bonus

### Hard Level (高级)
**Target Audience**: Expert players seeking maximum challenge

| Parameter | Value | Description |
|-----------|-------|-------------|
| Initial Drop Speed | 600ms | Fast piece falling |
| Speed Increment | 120ms per level | Aggressive speed increase |
| Lines Per Level | 8 lines | Quick level advancement |
| Enabled Pieces | All 7 pieces | Full complexity |
| Preview Pieces | 1 piece | Minimal preview |
| Ghost Piece | Disabled | No positioning assistance |
| Score Multiplier | 1.5x | Significant bonus |

**Special Features**:
- Rapid gameplay progression
- Minimal assistance features
- Maximum scoring potential
- Elite-level challenge

## User Interface Design

### Difficulty Selection Menu

```mermaid
graph LR
    A[Main Menu] --> B[Difficulty Selection Screen]
    B --> C[Easy Button<br/>初级<br/>🟢]
    B --> D[Medium Button<br/>中级<br/>🟡]
    B --> E[Hard Button<br/>高级<br/>🔴]
    C --> F[Game with Easy Config]
    D --> G[Game with Medium Config]
    E --> H[Game with Hard Config]
```

### In-Game Difficulty Display

| UI Element | Location | Content |
|------------|----------|---------|
| Difficulty Badge | Top-left corner | Colored badge showing current level |
| Speed Indicator | Score panel | Current drop interval display |
| Level Progress | Score panel | Progress to next speed increase |
| Preview Window | Sidebar | Configured number of next pieces |

## Implementation Components

### 1. Difficulty Configuration System

```javascript
// Difficulty configuration structure
const DIFFICULTY_CONFIGS = {
    easy: {
        name: "初级",
        initialSpeed: 1500,
        speedIncrement: 50,
        linesPerLevel: 15,
        previewCount: 2,
        ghostPiece: true,
        scoreMultiplier: 1.0
    },
    medium: {
        name: "中级", 
        initialSpeed: 1000,
        speedIncrement: 80,
        linesPerLevel: 10,
        previewCount: 1,
        ghostPiece: "optional",
        scoreMultiplier: 1.2
    },
    hard: {
        name: "高级",
        initialSpeed: 600,
        speedIncrement: 120,
        linesPerLevel: 8,
        previewCount: 1,
        ghostPiece: false,
        scoreMultiplier: 1.5
    }
};
```

### 2. Game Mechanics Modifications

#### Speed Progression System
- Replace current fixed speed increase with difficulty-specific progression
- Implement configurable lines-per-level thresholds
- Add speed increment calculations based on difficulty

#### Preview System Enhancement
- Extend current single-piece preview to support multiple pieces
- Implement dynamic preview count based on difficulty
- Maintain existing preview canvas with enhanced rendering

#### Ghost Piece Implementation
- Add semi-transparent piece rendering at drop position
- Implement optional toggle for medium difficulty
- Integrate with existing collision detection system

### 3. Scoring System Updates

#### Multiplier Application
```javascript
// Enhanced scoring calculation
calculateScore(linesCleared) {
    const baseScore = [0, 100, 300, 500, 800][linesCleared];
    const difficultyMultiplier = this.currentDifficulty.scoreMultiplier;
    return baseScore * this.level * difficultyMultiplier;
}
```

#### Achievement Integration
- Track best scores per difficulty level
- Implement difficulty-specific achievements
- Add completion badges for level milestones

## User Experience Flow

### Game Selection Process
1. **Main Menu Access**: Player clicks "新游戏" button
2. **Difficulty Selection**: Modal overlay presents three difficulty options
3. **Difficulty Preview**: Hover effects show key differences
4. **Selection Confirmation**: Click initiates game with chosen difficulty
5. **Game Initialization**: Game loads with appropriate configuration

### In-Game Experience
1. **Visual Indicators**: Difficulty badge and speed display
2. **Progressive Feedback**: Level advancement notifications
3. **Difficulty Switching**: Option to change difficulty mid-game (with score reset)
4. **Performance Tracking**: Separate statistics per difficulty level

## Testing Strategy

### Functional Testing
- **Configuration Loading**: Verify each difficulty applies correct parameters
- **Speed Progression**: Validate speed changes at level thresholds  
- **Preview System**: Test multiple piece preview rendering
- **Ghost Piece**: Verify positioning accuracy and visibility
- **Scoring**: Confirm multiplier calculations across all difficulties

### Usability Testing
- **Menu Navigation**: Smooth difficulty selection process
- **Visual Clarity**: Clear indication of current difficulty
- **Learning Curve**: Appropriate progression for each skill level
- **Accessibility**: Colorblind-friendly difficulty indicators

### Performance Testing
- **Rendering Efficiency**: Multiple preview pieces and ghost piece
- **Memory Usage**: Additional configuration storage
- **Responsiveness**: Maintained frame rates across all difficulties

## Technical Implementation Notes

### Code Structure Modifications
- Extend existing `TetrisGame` class with difficulty management
- Add configuration loader for difficulty settings  
- Implement preview system enhancement methods
- Create ghost piece rendering functionality

### Data Storage
- Store difficulty preferences in localStorage
- Maintain separate high scores per difficulty
- Track completion statistics by difficulty level

### Backward Compatibility
- Maintain existing game functionality as Medium difficulty baseline
- Preserve current control scheme and basic mechanics
- Ensure existing CSS styles accommodate new UI elements