* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.score-section {
    display: flex;
    gap: 30px;
}

.score-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f7fafc;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 80px;
}

.score-item .label {
    font-size: 0.9em;
    color: #718096;
    margin-bottom: 5px;
}

.score-item span:last-child {
    font-size: 1.8em;
    font-weight: bold;
    color: #2d3748;
}

.game-body {
    display: flex;
    gap: 30px;
    justify-content: center;
    align-items: flex-start;
}

.game-board-container {
    position: relative;
    display: flex;
    justify-content: center;
}

#gameBoard {
    border: 3px solid #4a5568;
    border-radius: 10px;
    background: #1a202c;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
}

.game-over.hidden {
    display: none;
}

.game-over-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.game-over-content h2 {
    color: #e53e3e;
    margin-bottom: 15px;
    font-size: 2em;
}

.game-over-content p {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #4a5568;
}

.game-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.next-piece {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.next-piece h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2em;
}

#nextPiece {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #1a202c;
}

.controls {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.controls h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2em;
    text-align: center;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
}

.control-item .key {
    background: #4a5568;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.control-item .action {
    color: #718096;
    font-size: 0.9em;
}

.game-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        padding: 15px;
        margin: 10px;
    }
    
    .game-body {
        flex-direction: column;
        align-items: center;
    }
    
    .game-sidebar {
        width: 100%;
        max-width: 300px;
    }
    
    .score-section {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    #gameBoard {
        width: 250px;
        height: 500px;
    }
}

@media (max-width: 480px) {
    .score-section {
        gap: 10px;
    }
    
    .score-item {
        padding: 10px 15px;
        min-width: 70px;
    }
    
    .score-item span:last-child {
        font-size: 1.5em;
    }
    
    #gameBoard {
        width: 200px;
        height: 400px;
    }
    
    .game-header h1 {
        font-size: 1.8em;
    }
}