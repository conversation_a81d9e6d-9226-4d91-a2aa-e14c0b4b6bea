class TetrisGame {
    constructor() {
        this.canvas = document.getElementById('gameBoard');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextPiece');
        this.nextCtx = this.nextCanvas.getContext('2d');
        
        // 游戏配置
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // 游戏状态
        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.isPaused = false;
        this.dropTimer = 0;
        this.dropInterval = 1000; // 1秒
        
        // 俄罗斯方块形状定义
        this.tetrominoes = {
            I: {
                shape: [
                    [0,0,0,0],
                    [1,1,1,1],
                    [0,0,0,0],
                    [0,0,0,0]
                ],
                color: '#00f5ff'
            },
            O: {
                shape: [
                    [1,1],
                    [1,1]
                ],
                color: '#ffff00'
            },
            T: {
                shape: [
                    [0,1,0],
                    [1,1,1],
                    [0,0,0]
                ],
                color: '#800080'
            },
            S: {
                shape: [
                    [0,1,1],
                    [1,1,0],
                    [0,0,0]
                ],
                color: '#00ff00'
            },
            Z: {
                shape: [
                    [1,1,0],
                    [0,1,1],
                    [0,0,0]
                ],
                color: '#ff0000'
            },
            J: {
                shape: [
                    [1,0,0],
                    [1,1,1],
                    [0,0,0]
                ],
                color: '#0000ff'
            },
            L: {
                shape: [
                    [0,0,1],
                    [1,1,1],
                    [0,0,0]
                ],
                color: '#ffa500'
            }
        };
        
        this.init();
    }
    
    init() {
        this.initBoard();
        this.bindEvents();
        this.generateNextPiece();
        this.spawnPiece();
        this.updateDisplay();
    }
    
    initBoard() {
        this.board = [];
        for (let row = 0; row < this.BOARD_HEIGHT; row++) {
            this.board[row] = [];
            for (let col = 0; col < this.BOARD_WIDTH; col++) {
                this.board[row][col] = 0;
            }
        }
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning || this.isPaused) return;
            
            switch(e.code) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.movePiece(-1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.movePiece(1, 0);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.movePiece(0, 1);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.rotatePiece();
                    break;
                case 'Space':
                    e.preventDefault();
                    this.hardDrop();
                    break;
            }
        });
        
        // 暂停按钮
        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.togglePause();
        });
        
        // 新游戏按钮
        document.getElementById('newGameBtn').addEventListener('click', () => {
            this.newGame();
        });
        
        // 重新开始按钮
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.newGame();
        });
        
        // 全局暂停键
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !this.gameRunning) return;
            if (e.code === 'Space') {
                e.preventDefault();
                if (this.gameRunning) {
                    this.togglePause();
                }
            }
        });
    }
    
    generateNextPiece() {
        const pieces = Object.keys(this.tetrominoes);
        const randomPiece = pieces[Math.floor(Math.random() * pieces.length)];
        this.nextPiece = {
            type: randomPiece,
            shape: this.tetrominoes[randomPiece].shape,
            color: this.tetrominoes[randomPiece].color,
            x: 0,
            y: 0
        };
    }
    
    spawnPiece() {
        this.currentPiece = this.nextPiece;
        this.currentPiece.x = Math.floor(this.BOARD_WIDTH / 2) - Math.floor(this.currentPiece.shape[0].length / 2);
        this.currentPiece.y = 0;
        
        this.generateNextPiece();
        
        // 检查游戏是否结束
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            this.gameOver();
            return;
        }
        
        this.drawNextPiece();
    }
    
    checkCollision(piece, offsetX, offsetY) {
        for (let row = 0; row < piece.shape.length; row++) {
            for (let col = 0; col < piece.shape[row].length; col++) {
                if (piece.shape[row][col]) {
                    const newX = piece.x + col + offsetX;
                    const newY = piece.y + row + offsetY;
                    
                    // 检查边界
                    if (newX < 0 || newX >= this.BOARD_WIDTH || newY >= this.BOARD_HEIGHT) {
                        return true;
                    }
                    
                    // 检查是否碰到其他方块
                    if (newY >= 0 && this.board[newY][newX]) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    movePiece(offsetX, offsetY) {
        if (!this.checkCollision(this.currentPiece, offsetX, offsetY)) {
            this.currentPiece.x += offsetX;
            this.currentPiece.y += offsetY;
            this.draw();
            return true;
        }
        return false;
    }
    
    rotatePiece() {
        const rotated = this.rotateMatrix(this.currentPiece.shape);
        const originalShape = this.currentPiece.shape;
        this.currentPiece.shape = rotated;
        
        // 尝试旋转，如果碰撞则恢复原状
        if (this.checkCollision(this.currentPiece, 0, 0)) {
            this.currentPiece.shape = originalShape;
        } else {
            this.draw();
        }
    }
    
    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = [];
        
        for (let col = 0; col < cols; col++) {
            rotated[col] = [];
            for (let row = rows - 1; row >= 0; row--) {
                rotated[col][rows - 1 - row] = matrix[row][col];
            }
        }
        return rotated;
    }
    
    hardDrop() {
        while (this.movePiece(0, 1)) {
            // 继续下降直到碰撞
        }
        this.placePiece();
    }
    
    placePiece() {
        // 将当前方块放置到游戏板上
        for (let row = 0; row < this.currentPiece.shape.length; row++) {
            for (let col = 0; col < this.currentPiece.shape[row].length; col++) {
                if (this.currentPiece.shape[row][col]) {
                    const boardX = this.currentPiece.x + col;
                    const boardY = this.currentPiece.y + row;
                    if (boardY >= 0) {
                        this.board[boardY][boardX] = this.currentPiece.color;
                    }
                }
            }
        }
        
        this.clearLines();
        this.spawnPiece();
        this.draw();
    }
    
    clearLines() {
        let linesCleared = 0;
        
        for (let row = this.BOARD_HEIGHT - 1; row >= 0; row--) {
            if (this.board[row].every(cell => cell !== 0)) {
                // 清除这一行
                this.board.splice(row, 1);
                this.board.unshift(new Array(this.BOARD_WIDTH).fill(0));
                linesCleared++;
                row++; // 重新检查同一行
            }
        }
        
        if (linesCleared > 0) {
            this.updateScore(linesCleared);
            this.showCelebration(linesCleared);
        }
    }
    
    updateScore(linesCleared) {
        const baseScore = [0, 100, 300, 500, 800];
        this.score += baseScore[linesCleared] * this.level;
        this.lines += linesCleared;
        
        // 升级逻辑
        const newLevel = Math.floor(this.lines / 10) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);
        }
        
        this.updateDisplay();
    }
    
    updateDisplay() {
        // 更新分数并添加动画效果
        const scoreElement = document.getElementById('score');
        const levelElement = document.getElementById('level');
        const linesElement = document.getElementById('lines');

        // 添加更新动画类
        scoreElement.classList.add('updated');
        levelElement.classList.add('updated');
        linesElement.classList.add('updated');

        // 更新数值
        scoreElement.textContent = this.score.toLocaleString();
        levelElement.textContent = this.level;
        linesElement.textContent = this.lines;

        // 移除动画类
        setTimeout(() => {
            scoreElement.classList.remove('updated');
            levelElement.classList.remove('updated');
            linesElement.classList.remove('updated');
        }, 500);
    }

    showCelebration(linesCleared) {
        const celebrations = {
            1: ['🎉', '👏', '✨'],
            2: ['🎊', '🔥', '💫', '⭐'],
            3: ['🚀', '💥', '🌟', '🎆'],
            4: ['🏆', '👑', '💎', '🌈', '🎯']
        };

        const emojis = celebrations[linesCleared] || celebrations[4];
        const gameContainer = document.querySelector('.game-container');

        // 创建多个庆祝emoji
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                const emoji = document.createElement('div');
                emoji.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                emoji.style.cssText = `
                    position: absolute;
                    font-size: 2em;
                    pointer-events: none;
                    z-index: 1000;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: celebrationFloat 2s ease-out forwards;
                `;

                gameContainer.appendChild(emoji);

                // 移除emoji
                setTimeout(() => {
                    if (emoji.parentNode) {
                        emoji.remove();
                    }
                }, 2000);
            }, i * 100);
        }

        // 添加庆祝动画CSS（如果还没有的话）
        if (!document.getElementById('celebration-styles')) {
            const style = document.createElement('style');
            style.id = 'celebration-styles';
            style.textContent = `
                @keyframes celebrationFloat {
                    0% {
                        transform: translateY(0) scale(0.5) rotate(0deg);
                        opacity: 1;
                    }
                    50% {
                        transform: translateY(-50px) scale(1.2) rotate(180deg);
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-100px) scale(0.8) rotate(360deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a202c';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制游戏板
        this.drawBoard();
        
        // 绘制当前方块
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }
        
        // 绘制网格
        this.drawGrid();
    }
    
    drawBoard() {
        for (let row = 0; row < this.BOARD_HEIGHT; row++) {
            for (let col = 0; col < this.BOARD_WIDTH; col++) {
                if (this.board[row][col]) {
                    this.ctx.fillStyle = this.board[row][col];
                    this.ctx.fillRect(
                        col * this.BLOCK_SIZE,
                        row * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );
                    
                    // 添加边框
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(
                        col * this.BLOCK_SIZE,
                        row * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );
                }
            }
        }
    }
    
    drawPiece(piece, context) {
        context.fillStyle = piece.color;
        for (let row = 0; row < piece.shape.length; row++) {
            for (let col = 0; col < piece.shape[row].length; col++) {
                if (piece.shape[row][col]) {
                    const x = (piece.x + col) * this.BLOCK_SIZE;
                    const y = (piece.y + row) * this.BLOCK_SIZE;
                    
                    context.fillRect(x, y, this.BLOCK_SIZE, this.BLOCK_SIZE);
                    
                    // 添加边框
                    context.strokeStyle = '#333';
                    context.lineWidth = 1;
                    context.strokeRect(x, y, this.BLOCK_SIZE, this.BLOCK_SIZE);
                }
            }
        }
    }
    
    drawGrid() {
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 0.5;
        
        // 垂直线
        for (let col = 0; col <= this.BOARD_WIDTH; col++) {
            this.ctx.beginPath();
            this.ctx.moveTo(col * this.BLOCK_SIZE, 0);
            this.ctx.lineTo(col * this.BLOCK_SIZE, this.canvas.height);
            this.ctx.stroke();
        }
        
        // 水平线
        for (let row = 0; row <= this.BOARD_HEIGHT; row++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, row * this.BLOCK_SIZE);
            this.ctx.lineTo(this.canvas.width, row * this.BLOCK_SIZE);
            this.ctx.stroke();
        }
    }
    
    drawNextPiece() {
        // 清空下一个方块的画布
        this.nextCtx.fillStyle = '#1a202c';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);
        
        if (this.nextPiece) {
            const blockSize = 20;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;
            
            this.nextCtx.fillStyle = this.nextPiece.color;
            
            for (let row = 0; row < this.nextPiece.shape.length; row++) {
                for (let col = 0; col < this.nextPiece.shape[row].length; col++) {
                    if (this.nextPiece.shape[row][col]) {
                        const x = offsetX + col * blockSize;
                        const y = offsetY + row * blockSize;
                        
                        this.nextCtx.fillRect(x, y, blockSize, blockSize);
                        this.nextCtx.strokeStyle = '#333';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(x, y, blockSize, blockSize);
                    }
                }
            }
        }
    }
    
    gameLoop() {
        if (!this.gameRunning || this.isPaused) return;
        
        this.dropTimer += 16; // 假设60FPS
        
        if (this.dropTimer >= this.dropInterval) {
            if (!this.movePiece(0, 1)) {
                this.placePiece();
            }
            this.dropTimer = 0;
        }
        
        this.draw();
    }
    
    start() {
        this.gameRunning = true;
        this.gameLoop();
        
        // 设置游戏循环
        this.gameInterval = setInterval(() => {
            this.gameLoop();
        }, 16);
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('pauseBtn');
        pauseBtn.textContent = this.isPaused ? '▶️ 继续' : '⏸️ 暂停';
    }
    
    gameOver() {
        this.gameRunning = false;
        clearInterval(this.gameInterval);
        
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOver').classList.remove('hidden');
    }
    
    newGame() {
        // 重置游戏状态
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.dropInterval = 1000;
        this.dropTimer = 0;
        this.isPaused = false;
        
        // 隐藏游戏结束界面
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('pauseBtn').textContent = '⏸️ 暂停';
        
        // 重新初始化
        this.initBoard();
        this.generateNextPiece();
        this.spawnPiece();
        this.updateDisplay();
        this.draw();
        
        // 开始游戏
        if (this.gameInterval) {
            clearInterval(this.gameInterval);
        }
        this.start();
    }
}

// 启动游戏
let game;
window.addEventListener('DOMContentLoaded', () => {
    game = new TetrisGame();
    game.draw();
    game.drawNextPiece();
    
    // 添加开始游戏的提示
    const startMessage = document.createElement('div');
    startMessage.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        font-size: 1.2em;
        z-index: 1000;
    `;
    startMessage.innerHTML = '点击"新游戏"开始!';
    document.querySelector('.game-board-container').appendChild(startMessage);
    
    // 点击新游戏时移除提示
    document.getElementById('newGameBtn').addEventListener('click', () => {
        if (startMessage.parentNode) {
            startMessage.remove();
        }
    });
});