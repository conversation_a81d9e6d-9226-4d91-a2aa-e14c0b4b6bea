* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
    animation: backgroundShift 10s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
    0% {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    100% {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    padding: 25px;
    max-width: 850px;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: containerGlow 3s ease-in-out infinite alternate;
}

@keyframes containerGlow {
    0% {
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    }
    100% {
        box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
    }
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.8em;
    background: linear-gradient(45deg, #667eea, #764ba2, #ff6b6b, #4ecdc4);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    animation: rainbowText 4s ease-in-out infinite;
    font-weight: bold;
}

@keyframes rainbowText {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.game-info {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.score-section {
    display: flex;
    gap: 30px;
}

.score-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    padding: 18px 22px;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    min-width: 90px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.score-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.score-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.score-item:hover::before {
    left: 100%;
}

.score-item .emoji {
    font-size: 1.5em;
    margin-bottom: 5px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

.score-item .label {
    font-size: 0.9em;
    color: #718096;
    margin-bottom: 5px;
}

.score-item span:last-child {
    font-size: 1.8em;
    font-weight: bold;
    color: #2d3748;
}

.game-body {
    display: flex;
    gap: 30px;
    justify-content: center;
    align-items: flex-start;
}

.game-board-container {
    position: relative;
    display: flex;
    justify-content: center;
}

#gameBoard {
    border: 4px solid #4a5568;
    border-radius: 15px;
    background: #1a202c;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    position: relative;
}

#gameBoard::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, #667eea, #764ba2, #ff6b6b, #4ecdc4);
    border-radius: 15px;
    z-index: -1;
    animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 100% 50%;
    }
}

.game-over {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
}

.game-over.hidden {
    display: none;
}

.game-over-content {
    background: linear-gradient(145deg, #ffffff, #f7fafc);
    padding: 35px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: gameOverPulse 1s ease-in-out;
}

@keyframes gameOverPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.game-over-content h2 {
    color: #e53e3e;
    margin-bottom: 15px;
    font-size: 2em;
}

.game-over-content p {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #4a5568;
}

.game-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.next-piece {
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    padding: 22px;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.next-piece:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.next-piece h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2em;
}

#nextPiece {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #1a202c;
}

.controls {
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    padding: 22px;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.controls:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.controls h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.2em;
    text-align: center;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
}

.control-item .key {
    background: #4a5568;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.control-item .action {
    color: #718096;
    font-size: 0.9em;
}

.game-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        padding: 15px;
        margin: 10px;
    }
    
    .game-body {
        flex-direction: column;
        align-items: center;
    }
    
    .game-sidebar {
        width: 100%;
        max-width: 300px;
    }
    
    .score-section {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    #gameBoard {
        width: 250px;
        height: 500px;
    }
}

@media (max-width: 480px) {
    .score-section {
        gap: 10px;
    }
    
    .score-item {
        padding: 10px 15px;
        min-width: 70px;
    }
    
    .score-item span:last-child {
        font-size: 1.5em;
    }
    
    #gameBoard {
        width: 200px;
        height: 400px;
    }
    
    .game-header h1 {
        font-size: 1.8em;
    }
}

/* 额外的动画效果和emoji样式 */
.control-item .action {
    transition: all 0.3s ease;
}

.control-item:hover .action {
    transform: translateX(5px);
    color: #4a5568;
}

.control-item .key {
    transition: all 0.3s ease;
}

.control-item:hover .key {
    transform: scale(1.1);
    background: linear-gradient(135deg, #667eea, #764ba2);
}

/* 游戏板发光效果 */
.game-board-container {
    position: relative;
}

.game-board-container::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
    border-radius: 25px;
    z-index: -1;
    animation: boardGlow 2s ease-in-out infinite alternate;
}

@keyframes boardGlow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

/* 分数数字动画 */
#score, #level, #lines {
    transition: all 0.3s ease;
}

#score.updated, #level.updated, #lines.updated {
    animation: scoreUpdate 0.5s ease-out;
}

@keyframes scoreUpdate {
    0% {
        transform: scale(1);
        color: #2d3748;
    }
    50% {
        transform: scale(1.2);
        color: #667eea;
    }
    100% {
        transform: scale(1);
        color: #2d3748;
    }
}

/* 悬浮粒子效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.3) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 1;
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
        opacity: 0.8;
    }
    66% {
        transform: translateY(20px) rotate(240deg);
        opacity: 0.6;
    }
}

/* 加载动画 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 32, 44, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    z-index: 100;
}

.loading-text {
    color: white;
    font-size: 1.5em;
    margin-bottom: 20px;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-blocks {
    display: flex;
    gap: 5px;
}

.loading-block {
    width: 20px;
    height: 20px;
    background: #667eea;
    animation: blockDance 1s ease-in-out infinite;
}

.loading-block:nth-child(2) {
    animation-delay: 0.2s;
    background: #764ba2;
}

.loading-block:nth-child(3) {
    animation-delay: 0.4s;
    background: #ff6b6b;
}

.loading-block:nth-child(4) {
    animation-delay: 0.6s;
    background: #4ecdc4;
}

@keyframes blockDance {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}